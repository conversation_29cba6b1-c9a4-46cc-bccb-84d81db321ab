"""
Invoice Processing Service - Hanterar fakturabehandlingsflödet
"""

import logging
import time
from typing import Dict, Any, Optional, List
from sqlalchemy.orm import Session
from uuid import UUID

from app.models import Invoice, Session as ProcessingSession, SessionLog
from app.services.llm_provider import get_llm_service, LLMResponse, reset_llm_service
from app.services.prompt_service import get_prompt_service
from app.services.ocr_service import OCRService
from app.services.vector_service import VectorService

logger = logging.getLogger(__name__)


class InvoiceProcessingService:
    """Service för att hantera fakturabehandlingsflödet"""
    
    def __init__(self, db: Session):
        self.db = db
        # Återställ LLM service för att säkerställa att den läser senaste konfigurationen
        reset_llm_service()
        self.llm_service = get_llm_service()
        self.prompt_service = get_prompt_service()
        self.ocr_service = OCRService()
        self.vector_service = VectorService()
    
    async def process_invoice(self, invoice_id: UUID, tenant_id: UUID) -> Dict[str, Any]:
        """<PERSON>ör hela fakturabehandlingsflödet - SIMPLIFIED VERSION"""
        # Hämta faktura
        invoice = self.db.query(Invoice).filter(
            Invoice.id == invoice_id,
            Invoice.tenant_id == tenant_id
        ).first()

        if not invoice:
            raise ValueError(f"Invoice {invoice_id} not found")

        # Skapa eller hämta session
        session = self._get_or_create_session(invoice)

        # Kör flödet steg för steg - NO ERROR HANDLING HERE
        # Let errors bubble up to Celery task for simple handling
        result = await self._run_processing_flow(session, invoice)

        return result
    
    def _get_or_create_session(self, invoice: Invoice) -> ProcessingSession:
        """Skapa eller hämta session för fakturan"""
        session = self.db.query(ProcessingSession).filter(
            ProcessingSession.invoice_id == invoice.id
        ).first()
        
        if not session:
            session = ProcessingSession(
                tenant_id=invoice.tenant_id,
                invoice_id=invoice.id,
                status="pending"
            )
            self.db.add(session)
            self.db.commit()
            self.db.refresh(session)
            logger.info(f"Created new session {session.id} for invoice {invoice.id}")
        
        return session
    
    async def _run_processing_flow(self, session: ProcessingSession, invoice: Invoice) -> Dict[str, Any]:
        """Kör hela bearbetningsflödet - SIMPLIFIED VERSION"""
        # NO session.status = "processing" here - handled in Celery task

        # Steg 1: Extrahera
        logger.info(f"Starting step 1: Extrahera for session {session.id}")
        await self._step_extrahera(session, invoice)
        logger.info(f"Completed step 1: Extrahera for session {session.id}")

        # Steg 2: Kontext
        logger.info(f"Starting step 2: Kontext for session {session.id}")
        await self._step_kontext(session)
        logger.info(f"Completed step 2: Kontext for session {session.id}")

        # Steg 3: Hitta konto
        logger.info(f"Starting step 3: Hitta konto for session {session.id}")
        await self._step_hitta_konto(session)
        logger.info(f"Completed step 3: Hitta konto for session {session.id}")

        # Kontrollera om session behöver manuell hantering (läs från databas)
        session = self.db.query(ProcessingSession).filter(ProcessingSession.id == session.id).first()
        logger.info(f"Checking session status for {session.id}: {session.status}")
        if session.status == "action-required":
            logger.info(f"Session {session.id} requires manual action, stopping automated processing")
            return {
                "session_id": str(session.id),
                "status": "action-required",
                "message": "Invoice processing requires manual action - no similar invoices found"
            }

        # Steg 4: Bokföra
        logger.info(f"Starting step 4: Bokföra for session {session.id}")
        await self._step_bokfora(session, invoice)
        logger.info(f"Completed step 4: Bokföra for session {session.id}")

        # Markera som klar
        session.status = "completed"
        session.current_step = None
        invoice.status = "completed"
        self.db.commit()

        return {
            "session_id": str(session.id),
            "status": "completed",
            "message": "Invoice processing completed successfully"
        }

        # NO ERROR HANDLING HERE - let errors bubble up to Celery task

    async def _handle_processing_error(self, session: ProcessingSession, invoice: Invoice, current_step: str, error: Exception):
        """Handle processing errors with proper database session management"""
        from app.database import recover_database_session, safe_commit

        # Spara ID:n innan vi försöker återställa objekten
        try:
            session_id = session.id
            invoice_id = invoice.id
        except Exception as detach_error:
            logger.warning(f"Session or invoice objects are detached: {detach_error}")
            # Om objekten är detached, försök hitta dem genom att söka i databasen
            session_id = None
            invoice_id = None

            # Försök hitta session genom att söka efter den som är i "processing" status
            try:
                from app.models.invoice import ProcessingSession, Invoice
                # Hitta alla sessions som är i processing status (borde bara vara en)
                processing_sessions = self.db.query(ProcessingSession).filter(
                    ProcessingSession.status == "processing"
                ).all()

                if processing_sessions:
                    # Ta den första (eller enda) processing session
                    session_id = processing_sessions[0].id
                    invoice_id = processing_sessions[0].invoice_id
                    logger.info(f"Found detached session {session_id} for invoice {invoice_id}")
                else:
                    logger.error("Could not find any processing sessions to update status for")
                    return
            except Exception as search_error:
                logger.error(f"Failed to find detached session: {search_error}")
                return

        try:
            # First, try to recover the database session
            recover_database_session(self.db)

            # Nu borde vi ha session_id och invoice_id
            if session_id is None or invoice_id is None:
                logger.error("Could not determine session_id or invoice_id for error handling")
                return

            # Hämta fresh objekten från databasen istället för att refresha
            from app.models.invoice import ProcessingSession, Invoice
            fresh_session = self.db.query(ProcessingSession).filter(ProcessingSession.id == session_id).first()
            fresh_invoice = self.db.query(Invoice).filter(Invoice.id == invoice_id).first()

            if not fresh_session or not fresh_invoice:
                logger.error(f"Could not find session {session_id} or invoice {invoice_id} in database")
                return

            # Update status in a new transaction
            fresh_session.status = "failed"
            fresh_session.error_message = str(error)
            fresh_session.failed_step = current_step
            fresh_invoice.status = "failed"
            fresh_invoice.processing_error = str(error)

            # Use safe commit to handle any remaining transaction issues
            if safe_commit(self.db, f"error status update for session {session_id}"):
                logger.info(f"Successfully updated session {session_id} status to failed")
            else:
                logger.error(f"Failed to commit error status update for session {session_id}")

        except Exception as commit_error:
            logger.error(f"Failed to update session status after error: {commit_error}")
            # If we can't update the status, at least ensure the session is rolled back
            try:
                self.db.rollback()
            except Exception:
                pass
    
    async def _step_extrahera(self, session: ProcessingSession, invoice: Invoice):
        """Steg 1: Extrahera fakturainformation"""
        session.current_step = "extrahera"
        self.db.commit()

        start_time = time.time()

        # Debug: Logga vilken modell som används
        provider_info = self.llm_service.get_provider_info()
        logger.info(f"Using LLM provider: {provider_info}")

        try:
            # Bearbeta filen baserat på typ
            text_content, image_data = self.ocr_service.process_file_for_llm(
                invoice.file_data,
                invoice.file_type
            )

            # Formatera prompt baserat på filtyp
            if text_content:
                # PDF med extraherad text
                system_prompt, user_prompt = self.prompt_service.format_prompt(
                    "extrahera",
                    file_data=text_content
                )
                # Skicka till LLM utan bild
                response = await self.llm_service.send_prompt(user_prompt, system_prompt)
            else:
                # Bild - skicka som base64 (kan vara original bild eller konverterad PDF)
                system_prompt, user_prompt = self.prompt_service.format_prompt(
                    "extrahera",
                    file_data="Se bifogad bild för fakturainnehåll"
                )
                # Bestäm rätt filtyp för LLM
                # Om original var PDF som konverterades till bild, använd 'jpeg'
                # Annars använd original filtyp
                llm_file_type = 'jpeg' if invoice.file_type.lower() == 'pdf' else invoice.file_type

                # Skicka till LLM med bild
                response = await self.llm_service.send_prompt(
                    user_prompt,
                    system_prompt,
                    image_data=image_data,
                    file_type=llm_file_type
                )
            
            if not response.success:
                raise Exception(f"LLM error in extrahera step: {response.error}")
            
            # Spara resultat till session-objektet
            # Konvertera dict till JSON-sträng för Text-kolumnen
            import json
            if isinstance(response.data, dict):
                session.extracted_data = json.dumps(response.data, ensure_ascii=False)
            else:
                session.extracted_data = str(response.data)
            session.extracted_reasoning = response.reasoning

            # Uppdatera token-summering
            if response.input_tokens is not None:
                session.total_input_tokens = (session.total_input_tokens or 0) + response.input_tokens
            if response.output_tokens is not None:
                session.total_output_tokens = (session.total_output_tokens or 0) + response.output_tokens

            # Försök commit session-ändringar
            try:
                self.db.commit()
            except Exception as commit_error:
                logger.error(f"Failed to commit session data for {session.id}: {commit_error}")
                try:
                    self.db.rollback()
                except Exception:
                    pass
                # Logga felet EFTER rollback
                self._log_step(
                    session=session,
                    step_name="extrahera",
                    prompt_sent=user_prompt,
                    llm_response=response.data,
                    reasoning=response.reasoning,
                    execution_time_ms=(time.time() - start_time) * 1000,
                    success=False,
                    error_message=f"Database commit failed: {commit_error}",
                    input_tokens=response.input_tokens,
                    output_tokens=response.output_tokens
                )
                raise commit_error

            # Logga framgångsrikt steg
            self._log_step(
                session=session,
                step_name="extrahera",
                prompt_sent=user_prompt,
                llm_response=response.data,
                reasoning=response.reasoning,
                execution_time_ms=(time.time() - start_time) * 1000,
                success=True,
                input_tokens=response.input_tokens,
                output_tokens=response.output_tokens
            )

            logger.info(f"Completed extrahera step for session {session.id}")
            
        except Exception as e:
            # Log the error - _log_step will handle database session recovery
            self._log_step(
                session=session,
                step_name="extrahera",
                prompt_sent=user_prompt if 'user_prompt' in locals() else "",
                llm_response="",
                reasoning="",
                execution_time_ms=(time.time() - start_time) * 1000,
                success=False,
                error_message=str(e),
                input_tokens=None,
                output_tokens=None
            )
            raise
    
    async def _step_kontext(self, session: ProcessingSession):
        """Steg 2: Skapa kontext"""
        session.current_step = "kontext"
        self.db.commit()
        
        start_time = time.time()
        
        try:
            # Formatera prompt - konvertera JSON-sträng tillbaka till dict om nödvändigt
            extracted_data = session.extracted_data
            if extracted_data and isinstance(extracted_data, str):
                try:
                    import json
                    extracted_data = json.loads(extracted_data)
                except json.JSONDecodeError:
                    # Om det inte är JSON, använd som sträng
                    pass

            system_prompt, user_prompt = self.prompt_service.format_prompt(
                "kontext",
                extracted_data=extracted_data
            )
            
            # Skicka till LLM
            response = await self.llm_service.send_prompt(user_prompt, system_prompt)
            
            if not response.success:
                raise Exception(f"LLM error in kontext step: {response.error}")
            
            # Logga steget FÖRST
            self._log_step(
                session=session,
                step_name="kontext",
                prompt_sent=user_prompt,
                llm_response=response.data,
                reasoning=response.reasoning,
                execution_time_ms=(time.time() - start_time) * 1000,
                success=True,
                input_tokens=response.input_tokens,
                output_tokens=response.output_tokens
            )

            # Nu försök spara resultat till session-objektet
            try:
                session.context_data = response.data
                session.context_reasoning = response.reasoning

                # Uppdatera token-summering
                if response.input_tokens is not None:
                    session.total_input_tokens = (session.total_input_tokens or 0) + response.input_tokens
                if response.output_tokens is not None:
                    session.total_output_tokens = (session.total_output_tokens or 0) + response.output_tokens

                # Försök commit session-ändringar
                self.db.commit()
                logger.info(f"Completed kontext step for session {session.id}")

            except Exception as commit_error:
                logger.warning(f"Failed to save session data for {session.id}, but step completed successfully: {commit_error}")
                try:
                    self.db.rollback()
                except Exception:
                    pass
            
        except Exception as e:
            # Log the error - _log_step will handle database session recovery
            self._log_step(
                session=session,
                step_name="kontext",
                prompt_sent=user_prompt if 'user_prompt' in locals() else "",
                llm_response="",
                reasoning="",
                execution_time_ms=(time.time() - start_time) * 1000,
                success=False,
                error_message=str(e),
                input_tokens=None,
                output_tokens=None
            )
            raise
    
    async def _step_hitta_konto(self, session: ProcessingSession):
        """Steg 3: Hitta lämpliga konton med RAG"""
        logger.info(f"🔍 ENTERING _step_hitta_konto for session {session.id}")

        try:
            session.current_step = "hitta_konto"
            self.db.commit()
            logger.info(f"✅ Set current_step to hitta_konto for session {session.id}")

            start_time = time.time()
            user_prompt = ""
            # Hämta RAG-kontext från liknande fakturor och tidigare kontobeslut
            # Använd try-catch för att hantera eventuella databasfel
            try:
                logger.info(f"🔍 Getting RAG context for session {session.id}")
                rag_context = await self._get_account_rag_context(session)
                logger.info(f"📄 RAG context result for session {session.id}: {rag_context[:100]}...")

                # Kontrollera om ingen liknande data hittades - trigga HITL flödet
                if (rag_context == "Ingen tidigare kontext eller kontobeslut hittades för liknande fakturor." or
                    "Ingen RAG-kontext tillgänglig" in rag_context or
                    "Fel vid hämtning av RAG-kontext" in rag_context):
                    logger.info(f"❌ No RAG context found for session {session.id}, triggering HITL workflow")

                    # Sätt session status till action-required
                    logger.info(f"🚨 Setting session {session.id} status to action-required")
                    session.status = "action-required"
                    session.account_reasoning = "Ingen liknande faktura eller tidigare kontobeslut hittades. Manuell granskning krävs."

                    # Skapa ActionItem för manuell hantering
                    logger.info(f"📝 Creating action item for session {session.id}")
                    await self._create_action_item_for_session(session)
                    logger.info(f"✅ Action item created for session {session.id}")

                    # Uppdatera session i databasen och refresh objektet
                    self.db.commit()
                    self.db.refresh(session)
                    logger.info(f"🔄 Session {session.id} refreshed from database, status: {session.status}")

                    # Logga steget som framgångsrikt men med action-required status
                    self._log_step(
                        session=session,
                        step_name="hitta_konto",
                        prompt_sent="",
                        llm_response="",
                        reasoning="Ingen RAG-kontext hittades, HITL-flöde aktiverat",
                        execution_time_ms=(time.time() - start_time) * 1000,
                        success=True,
                        input_tokens=0,
                        output_tokens=0
                    )

                    self.db.commit()
                    logger.info(f"Session {session.id} set to action-required status")
                    return  # Avsluta steget här, fortsätt inte till LLM

            except Exception as rag_error:
                logger.error(f"Failed to get RAG context for session {session.id}: {rag_error}")
                # När RAG-kontexten failar, trigga HITL-flödet
                logger.info(f"RAG context failed for session {session.id}, triggering HITL workflow")

                # Sätt session status till action-required
                session.status = "action-required"
                session.account_reasoning = f"Fel vid hämtning av RAG-kontext: {str(rag_error)}. Manuell granskning krävs."

                # Skapa ActionItem för manuell hantering
                await self._create_action_item_for_session(session)

                # Logga steget som framgångsrikt men med action-required status
                self._log_step(
                    session=session,
                    step_name="hitta_konto",
                    prompt_sent="",
                    llm_response="",
                    reasoning=f"RAG-kontext failade: {str(rag_error)}, HITL-flöde aktiverat",
                    execution_time_ms=(time.time() - start_time) * 1000,
                    success=True,
                    input_tokens=0,
                    output_tokens=0
                )

                self.db.commit()
                logger.info(f"Session {session.id} set to action-required status due to RAG failure")
                return  # Avsluta steget här, fortsätt inte till LLM

            # Formatera prompt med RAG-kontext
            system_prompt, user_prompt = self.prompt_service.format_prompt(
                "hitta_konto",
                context_data=session.context_data,
                rag_context=rag_context
            )

            # Skicka till LLM
            response = await self.llm_service.send_prompt(user_prompt, system_prompt)

            if not response.success:
                raise Exception(f"LLM error in hitta_konto step: {response.error}")

            # Försök att parsa JSON data
            try:
                import json
                account_data = json.loads(response.data) if isinstance(response.data, str) else response.data
            except json.JSONDecodeError:
                # Om det inte är JSON, spara som text
                account_data = {"raw_response": response.data}

            # Spara resultat till session-objektet
            session.account_data = account_data
            session.account_reasoning = response.reasoning

            # Uppdatera token-summering
            if response.input_tokens is not None:
                session.total_input_tokens = (session.total_input_tokens or 0) + response.input_tokens
            if response.output_tokens is not None:
                session.total_output_tokens = (session.total_output_tokens or 0) + response.output_tokens

            # Försök commit session-ändringar
            try:
                self.db.commit()
            except Exception as commit_error:
                logger.error(f"Failed to commit session data for {session.id}: {commit_error}")
                # Om commit failar, försök rollback och logga felet
                try:
                    self.db.rollback()
                except Exception:
                    pass
                # Logga felet EFTER rollback
                self._log_step(
                    session=session,
                    step_name="hitta_konto",
                    prompt_sent=user_prompt,
                    llm_response=response.data,
                    reasoning=response.reasoning,
                    execution_time_ms=(time.time() - start_time) * 1000,
                    success=False,
                    error_message=f"Database commit failed: {commit_error}",
                    input_tokens=response.input_tokens,
                    output_tokens=response.output_tokens
                )
                raise commit_error

            # Logga framgångsrikt steg
            self._log_step(
                session=session,
                step_name="hitta_konto",
                prompt_sent=user_prompt,
                llm_response=response.data,
                reasoning=response.reasoning,
                execution_time_ms=(time.time() - start_time) * 1000,
                success=True,
                input_tokens=response.input_tokens,
                output_tokens=response.output_tokens
            )

            logger.info(f"✅ COMPLETED hitta_konto step for session {session.id}, session status: {session.status}")

        except Exception as e:
            logger.error(f"❌ EXCEPTION in _step_hitta_konto for session {session.id}: {str(e)}", exc_info=True)
            # Log the error - _log_step will handle database session recovery
            self._log_step(
                session=session,
                step_name="hitta_konto",
                prompt_sent=user_prompt,
                llm_response="",
                reasoning="",
                execution_time_ms=(time.time() - start_time) * 1000,
                success=False,
                error_message=str(e),
                input_tokens=None,
                output_tokens=None
            )
            logger.error(f"❌ RAISING EXCEPTION from _step_hitta_konto for session {session.id}")
            raise

    async def _get_account_rag_context(self, session: ProcessingSession) -> str:
        """Hämta RAG-kontext för kontoval från liknande fakturor och tidigare kontobeslut"""
        logger.info(f"🔍 ENTERING _get_account_rag_context for session {session.id}")
        rag_parts = []
        similar_contexts = []
        similar_accounting_entries = []

        try:
            # 1. Hämta kontext från liknande fakturor
            if session.context_data:
                try:
                    similar_contexts = await self.vector_service.get_similar_contexts(
                        db=self.db,
                        tenant_id=session.invoice.tenant_id,
                        query_content=session.context_data,
                        limit=3
                    )

                    if similar_contexts:
                        rag_parts.append("## Kontext från liknande fakturor:")
                        for i, context in enumerate(similar_contexts, 1):
                            rag_parts.append(f"### Liknande faktura {i}:")
                            rag_parts.append(context)
                            rag_parts.append("")
                        logger.info(f"Found {len(similar_contexts)} similar contexts for session {session.id}")
                    else:
                        logger.info(f"No similar contexts found for session {session.id}")
                except Exception as e:
                    logger.warning(f"Failed to get similar contexts: {e}")
                    # Continue without similar contexts

            # 2. Hämta tidigare kontobeslut för liknande leverantörer/kategorier
            try:
                similar_accounting_entries = await self._get_similar_accounting_entries(session)

                if similar_accounting_entries:
                    rag_parts.append("## Tidigare kontobeslut för liknande fakturor:")
                    for entry_group in similar_accounting_entries:
                        supplier = entry_group.get('supplier_name', 'Okänd leverantör')
                        rag_parts.append(f"### {supplier}:")

                        for entry in entry_group.get('entries', []):
                            amount_info = ""
                            if entry.get('debit_amount'):
                                amount_info = f" (Debet: {entry['debit_amount']} kr)"
                            elif entry.get('credit_amount'):
                                amount_info = f" (Kredit: {entry['credit_amount']} kr)"

                            confidence_info = f" [Konfidensgrad: {entry.get('confidence_score', 0):.2f}]"

                            rag_parts.append(
                                f"- Konto {entry['account_code']} - {entry['account_name']}"
                                f"{amount_info}{confidence_info}"
                            )

                            if entry.get('description'):
                                rag_parts.append(f"  Beskrivning: {entry['description']}")

                        rag_parts.append("")
                    logger.info(f"Found {len(similar_accounting_entries)} accounting entry groups for session {session.id}")
                else:
                    logger.info(f"No similar accounting entries found for session {session.id}")
            except Exception as e:
                logger.warning(f"Failed to get similar accounting entries: {e}")
                # Continue without accounting entries

            # 3. Sammanställ RAG-kontext
            if rag_parts:
                rag_context = "\n".join(rag_parts)
                logger.info(f"Generated RAG context with {len(similar_contexts)} similar contexts and {len(similar_accounting_entries)} accounting entry groups")
                return rag_context
            else:
                logger.info("No RAG context found for account selection")
                return "Ingen tidigare kontext eller kontobeslut hittades för liknande fakturor."

        except Exception as e:
            logger.error(f"Error generating RAG context for account selection: {e}")
            # Return a safe fallback instead of raising
            return "Fel vid hämtning av RAG-kontext för kontoval. Fortsätter utan historisk kontext."

    async def _get_similar_accounting_entries(self, session: ProcessingSession) -> List[Dict]:
        """Hämta tidigare kontobeslut för liknande leverantörer och kategorier"""
        try:
            from app.models.invoice import AccountingEntry

            # Hämta leverantörens namn från nuvarande faktura
            current_supplier = session.invoice.supplier_name

            # Sök efter tidigare kontobeslut för samma leverantör
            supplier_entries = []
            if current_supplier:
                try:
                    supplier_query = self.db.query(AccountingEntry).join(Invoice).filter(
                        Invoice.tenant_id == session.invoice.tenant_id,
                        Invoice.supplier_name.ilike(f"%{current_supplier}%"),
                        AccountingEntry.is_validated == True,  # Endast validerade poster
                        Invoice.id != session.invoice.id  # Exkludera nuvarande faktura
                    ).order_by(AccountingEntry.confidence_score.desc()).limit(10)

                    supplier_entries = supplier_query.all()
                except Exception as query_error:
                    logger.warning(f"Failed to query supplier entries for {current_supplier}: {query_error}")
                    # Continue with empty list

            # Gruppera efter leverantör
            grouped_entries = {}
            for entry in supplier_entries:
                supplier_name = entry.invoice.supplier_name
                if supplier_name not in grouped_entries:
                    grouped_entries[supplier_name] = {
                        'supplier_name': supplier_name,
                        'entries': []
                    }

                grouped_entries[supplier_name]['entries'].append({
                    'account_code': entry.account_code,
                    'account_name': entry.account_name,
                    'debit_amount': entry.debit_amount,
                    'credit_amount': entry.credit_amount,
                    'description': entry.description,
                    'confidence_score': entry.confidence_score
                })

            # Konvertera till lista och begränsa antal poster per leverantör
            result = []
            for supplier_data in grouped_entries.values():
                # Begränsa till max 5 poster per leverantör
                supplier_data['entries'] = supplier_data['entries'][:5]
                result.append(supplier_data)

            # Begränsa totalt antal leverantörer
            result = result[:3]

            logger.info(f"Found {len(result)} supplier groups with similar accounting entries")
            return result

        except Exception as e:
            logger.error(f"Error fetching similar accounting entries: {e}")
            return []
    
    async def _step_bokfora(self, session: ProcessingSession, invoice: Invoice):
        """Steg 4: Bokföra (DEV mode - ingen faktisk bokföring än)"""
        session.current_step = "bokfora"
        self.db.commit()

        start_time = time.time()

        try:
            # DEV mode - ingen faktisk bokföring implementerad än
            logger.info("DEV mode: Bokföring step - ingen faktisk ERP integration implementerad än")

            # Bestäm ERP system baserat på import_typ (för framtida användning)
            erp_system = "manual" if invoice.import_typ == "manuell" else invoice.import_typ

            # Skapa en enkel placeholder för booking_result
            booking_result = {
                "status": "dev_mode",
                "message": "DEV mode - bokföring ej implementerad",
                "erp_system": erp_system,
                "dev_mode": True,
                "timestamp": time.time()
            }

            # Logga steget
            self._log_step(
                session=session,
                step_name="bokfora",
                prompt_sent="DEV mode - ingen prompt skickad",
                llm_response="DEV mode - ingen LLM-respons",
                reasoning="DEV mode - bokföring ej implementerad",
                execution_time_ms=(time.time() - start_time) * 1000,
                success=True,
                input_tokens=0,
                output_tokens=0
            )

            # Spara resultat till session-objektet
            try:
                session.booking_result = booking_result
                session.booking_reasoning = "DEV mode - ingen faktisk bokföring utförd"

                # Inga tokens används i DEV mode
                # session.total_input_tokens och session.total_output_tokens behöver inte uppdateras

                # Commit session-ändringar
                self.db.commit()
                logger.info(f"Completed bokfora step (DEV mode) for session {session.id}")

            except Exception as commit_error:
                logger.warning(f"Failed to save session data for {session.id}, but step completed successfully: {commit_error}")
                try:
                    self.db.rollback()
                except Exception:
                    pass

        except Exception as e:
            # Log the error
            self._log_step(
                session=session,
                step_name="bokfora",
                prompt_sent="DEV mode - error occurred",
                llm_response="",
                reasoning="",
                execution_time_ms=(time.time() - start_time) * 1000,
                success=False,
                error_message=str(e),
                input_tokens=0,
                output_tokens=0
            )
            raise
    
    def _log_step(self, session: ProcessingSession, step_name: str, prompt_sent: str,
                  llm_response: str, reasoning: str, execution_time_ms: float,
                  success: bool, error_message: Optional[str] = None, input_tokens: Optional[int] = None,
                  output_tokens: Optional[int] = None):
        """Logga ett bearbetningssteg"""
        from app.database import recover_database_session, safe_commit

        # Spara session-ID:t tidigt för att undvika DetachedInstanceError
        try:
            session_id = session.id
            tenant_id = session.tenant_id
        except Exception:
            logger.error(f"Could not access session attributes for logging step {step_name}")
            return

        try:
            import json

            # Konvertera llm_response till sträng om det är ett dict
            if isinstance(llm_response, dict):
                llm_response = json.dumps(llm_response, ensure_ascii=False)
            elif llm_response is None:
                llm_response = ""

            # Om detta är en error-logg efter rollback, behöver vi återställa session-tillståndet
            if not success:
                try:
                    # Försök återställa database session om den är i dåligt tillstånd
                    recover_database_session(self.db)
                    # Refresh session object från databasen för att få clean state
                    self.db.refresh(session)
                except Exception as recovery_error:
                    logger.warning(f"Could not recover database session for error logging: {recovery_error}")
                    # Om vi inte kan återställa, logga bara till fil och returnera
                    logger.error(f"Step {step_name} failed for session {session_id}: {error_message}")
                    return

            log_entry = SessionLog(
                tenant_id=tenant_id,
                session_id=session_id,
                step_name=step_name,
                prompt_sent=prompt_sent,
                llm_response=str(llm_response),
                reasoning=reasoning,
                execution_time_ms=execution_time_ms,
                success=success,
                error_message=error_message,
                input_tokens=input_tokens,
                output_tokens=output_tokens
            )

            self.db.add(log_entry)

            # Använd safe_commit för både success och error-loggar
            if not safe_commit(self.db, f"log for step {step_name}"):
                logger.error(f"Failed to commit log for step {step_name} in session {session_id}")

        except Exception as log_error:
            # Don't let logging errors break the main flow
            logger.error(f"Failed to log step {step_name} for session {session_id}: {log_error}")
            # För error-loggar, försök inte rollback eftersom vi redan är i error-hantering
            if success:
                try:
                    self.db.rollback()
                except Exception:
                    pass

    async def _create_action_item_for_session(self, session: ProcessingSession):
        """Skapa ActionItem för session som behöver manuell hantering"""
        try:
            from app.models.action_item import ActionItem
            from app.models.user import TenantUser

            # Hitta en användare i samma tenant som kan hantera action items
            # För nu tar vi första aktiva användaren, men detta kan förbättras
            tenant_user = self.db.query(TenantUser).filter(
                TenantUser.tenant_id == session.invoice.tenant_id,
                TenantUser.is_active == True
            ).first()

            if not tenant_user:
                logger.warning(f"No active users found for tenant {session.invoice.tenant_id}, cannot create action item")
                return

            # Skapa ActionItem
            action_item = ActionItem(
                tenant_id=session.invoice.tenant_id,
                user_id=tenant_user.user_id,
                invoice_id=session.invoice.id,
                session_id=session.id,
                title=f"Manuell kontoval krävs - {session.invoice.supplier_name or 'Okänd leverantör'}",
                description=f"Ingen liknande faktura hittades för automatisk kontoval. Faktura från {session.invoice.supplier_name or 'okänd leverantör'} med belopp {session.invoice.total_amount or 'okänt'} kr behöver manuell granskning och kontoval.",
                priority="medium",
                category="manual_entry"
            )

            self.db.add(action_item)
            logger.info(f"Created action item {action_item.id} for session {session.id}")

        except Exception as e:
            logger.error(f"Failed to create action item for session {session.id}: {e}")
            # Låt inte detta stoppa huvudflödet
